# Document Analyzer for Investment Firm

A Python script that analyzes PDF documents from URLs and generates structured JSON summaries specifically designed for investment analysis of NSE/BSE announcements and corporate communications.

## Features

- **Automatic Document Classification**: Identifies document types (earnings calls, quarterly results, M&A, press releases, etc.)
- **AI-Powered Analysis**: Uses OpenAI GPT-4 to extract key information relevant to investment decisions
- **Structured JSON Output**: Provides consistent, machine-readable summaries
- **Investment-Focused**: Tailored for equity research and long-term investment analysis
- **Concise Summaries**: Follows 50-word guideline while preserving critical details

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set OpenAI API Key**:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key-here"
   ```
   
   Or on Windows:
   ```cmd
   set OPENAI_API_KEY=your-openai-api-key-here
   ```

## Usage

### Command Line Usage

```bash
python document_analyzer.py <pdf_url>
```

Example:
```bash
python document_analyzer.py "https://example.com/company-earnings-call.pdf"
```

### Python Script Usage

```python
from document_analyzer import DocumentAnalyzer
import os

# Initialize analyzer
analyzer = DocumentAnalyzer(os.getenv("OPENAI_API_KEY"))

# Analyze document
result = analyzer.analyze_document("https://example.com/document.pdf")
print(result)
```

## Output Format

The script returns a JSON object with the following structure:

```json
{
  "document_type": "earnings_call|quarterly_results|merger_acquisition|annual_report|press_release|general_announcement",
  "company_name": "Company Name Ltd",
  "summary": "Concise 50-word summary of key information",
  "key_details": {
    "quarter": "Q2 FY2024",
    "revenue": "₹1,000 Cr (+15% YoY)",
    "ebitda": "₹200 Cr (+20% YoY)",
    "pat": "₹150 Cr (+25% YoY)",
    "management_commentary": "Strong performance driven by...",
    "call_date": "2024-01-15 15:00 IST",
    "registration_url": "https://example.com/register"
  },
  "analysis_date": "2024-01-10T10:30:00",
  "source_url": "https://example.com/document.pdf",
  "status": "success"
}
```

## Document Types Supported

1. **Earnings Call Announcements**
   - Quarter and Financial Year
   - Date and time
   - Registration URL

2. **Quarterly Results**
   - Financial period
   - Revenue, EBITDA, PAT with YoY growth
   - Management commentary

3. **M&A Announcements**
   - Target company details
   - Deal terms and valuation
   - Related party transaction status
   - Acquisition purpose

4. **Annual Reports**
   - Financial year
   - Key highlights

5. **Press Releases**
   - Main message (no fluff)
   - Business implications

## Error Handling

The script includes comprehensive error handling:
- Network issues during PDF download
- PDF parsing errors
- AI analysis failures
- Invalid URLs

Failed analyses return error status with details:

```json
{
  "status": "error",
  "error_message": "Description of the error",
  "source_url": "https://example.com/document.pdf",
  "analysis_date": "2024-01-10T10:30:00"
}
```

## Requirements

- Python 3.7+
- OpenAI API key
- Internet connection for PDF downloads and AI analysis

## Dependencies

- `requests`: For downloading PDFs from URLs
- `PyPDF2`: For extracting text from PDF files
- `openai`: For AI-powered document analysis

## Notes

- The script is optimized for NSE/BSE announcement formats
- Summaries target 50 words but may exceed for critical financial data
- AI analysis uses GPT-4 for highest quality insights
- All timestamps are in ISO format
- Company name extraction uses pattern matching for Indian corporate formats
