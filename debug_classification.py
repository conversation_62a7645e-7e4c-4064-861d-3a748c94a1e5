#!/usr/bin/env python3
"""
Debug script to test document classification
"""

import os
from document_analyzer import <PERSON>umentAnaly<PERSON>

def debug_classification(pdf_url: str):
    """Debug the classification of a document"""
    
    api_key = "********************************************************************************************************************************************************************"
    
    analyzer = DocumentAnalyzer(api_key)
    
    try:
        # Download and extract text
        print(f"Downloading: {pdf_url}")
        pdf_content = analyzer.download_pdf(pdf_url)
        print(f"Downloaded {len(pdf_content)} bytes")
        
        text = analyzer.extract_text_from_pdf(pdf_content)
        print(f"Extracted {len(text)} characters")
        
        # Debug classification
        print("\n" + "="*50)
        print("CLASSIFICATION DEBUG")
        print("="*50)
        
        # Show first 500 characters
        header_text = text.lower()[:500]
        print(f"First 500 characters (lowercase):")
        print(repr(header_text))
        print()
        
        # Test each classification pattern
        print("Classification tests:")
        
        # Press release patterns
        pr_patterns = ['press release', 'media release', 'pr22072025', 'press note']
        pr_found = [pattern for pattern in pr_patterns if pattern in header_text]
        print(f"Press Release patterns found: {pr_found}")
        
        # Earnings call patterns
        ec_patterns = ['earnings call', 'investor call', 'conference call']
        ec_found = [pattern for pattern in ec_patterns if pattern in text.lower()]
        print(f"Earnings Call patterns found: {ec_found}")
        
        # Results patterns
        results_patterns = ['quarterly results', 'financial results', 'unaudited results', 'q1 fy', 'q2 fy', 'q3 fy', 'q4 fy']
        results_found = [pattern for pattern in results_patterns if pattern in text.lower()]
        print(f"Results patterns found: {results_found}")
        
        # M&A patterns
        ma_patterns = ['acquisition of', 'merger with', 'amalgamation with', 'takeover of', 'buyout of']
        ma_found = [pattern for pattern in ma_patterns if pattern in text.lower()]
        print(f"M&A patterns found: {ma_found}")
        
        # Final classification
        doc_type = analyzer.classify_document_type(text)
        print(f"\nFinal classification: {doc_type}")
        
        # Company name extraction
        company_name = analyzer.extract_company_name(text, pdf_url)
        print(f"Company name: {company_name}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python debug_classification.py <pdf_url>")
        sys.exit(1)
    
    debug_classification(sys.argv[1])
