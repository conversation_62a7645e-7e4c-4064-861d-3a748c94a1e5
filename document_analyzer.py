#!/usr/bin/env python3
"""
Document Analyzer for Investment Firm
Analyzes PDF documents from URLs and generates structured JSON summaries
for NSE/BSE announcements and corporate communications.
"""

import json
import re
import requests
import PyPDF2
from io import BytesIO
from datetime import datetime
from typing import Dict, Any, Optional
import openai
from dataclasses import dataclass
import logging
import urllib3

# Disable SSL warnings for NSE/BSE sites
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DocumentSummary:
    """Structure for document summary output"""
    document_type: str
    company_name: str
    summary: str
    key_details: Dict[str, Any]
    analysis_date: str
    source_url: str

class DocumentAnalyzer:
    """Main class for analyzing investment documents"""
    
    def __init__(self, openai_api_key: str):
        """Initialize with OpenAI API key"""
        self.openai_client = openai.OpenAI(api_key=openai_api_key)
        
    def download_pdf(self, url: str) -> bytes:
        """Download PDF from URL with proper headers for NSE/BSE"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/pdf,application/octet-stream,*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }

        session = requests.Session()
        session.headers.update(headers)

        try:
            # Try with longer timeout and retries for NSE/BSE
            for attempt in range(3):
                try:
                    logger.info(f"Download attempt {attempt + 1}/3")
                    response = session.get(url, timeout=120, stream=True)
                    response.raise_for_status()

                    # Download in chunks to handle large files
                    content = b""
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            content += chunk

                    logger.info(f"Successfully downloaded {len(content)} bytes")
                    return content

                except requests.exceptions.Timeout:
                    logger.warning(f"Timeout on attempt {attempt + 1}")
                    if attempt == 2:  # Last attempt
                        raise
                    continue
                except requests.exceptions.RequestException as e:
                    logger.warning(f"Request failed on attempt {attempt + 1}: {e}")
                    if attempt == 2:  # Last attempt
                        raise
                    continue

        except requests.RequestException as e:
            logger.error(f"Failed to download PDF from {url}: {e}")
            # Try alternative approach for NSE/BSE URLs
            if 'nsearchives.nseindia.com' in url or 'bseindia.com' in url:
                logger.info("Trying alternative download method for NSE/BSE...")
                try:
                    return self._download_with_selenium_fallback(url)
                except Exception as selenium_error:
                    logger.error(f"Selenium fallback also failed: {selenium_error}")
            raise

    def _download_with_selenium_fallback(self, url: str) -> bytes:
        """Fallback method using requests with additional NSE-specific handling"""
        import time

        # Additional headers specifically for NSE
        nse_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }

        session = requests.Session()
        session.headers.update(nse_headers)

        # Add a small delay to avoid being blocked
        time.sleep(2)

        response = session.get(url, timeout=180, allow_redirects=True, verify=False)
        response.raise_for_status()

        return response.content
    
    def extract_text_from_pdf(self, pdf_content: bytes) -> str:
        """Extract text content from PDF bytes"""
        try:
            pdf_file = BytesIO(pdf_content)
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Failed to extract text from PDF: {e}")
            raise
    
    def classify_document_type(self, text: str) -> str:
        """Classify the type of document based on content"""
        text_lower = text.lower()

        # Check first 500 characters for document type indicators (more reliable)
        header_text = text_lower[:500]

        # Document type patterns - order matters! Check specific types first
        # Check for press release first (most common and often contains other keywords)
        if any(term in header_text for term in ['press release', 'media release', 'pr22072025', 'press note']):
            return "press_release"
        elif any(term in text_lower for term in ['earnings call', 'investor call', 'conference call']):
            return "earnings_call"
        elif any(term in text_lower for term in ['annual report', 'annual accounts']):
            return "annual_report"
        elif any(term in text_lower for term in ['quarterly results', 'financial results', 'unaudited results', 'q1 fy', 'q2 fy', 'q3 fy', 'q4 fy']):
            return "quarterly_results"
        # Check for actual M&A transactions (not just partnerships)
        elif any(term in text_lower for term in ['acquisition of', 'merger with', 'amalgamation with', 'takeover of', 'buyout of']):
            return "merger_acquisition"
        else:
            return "general_announcement"
    
    def extract_company_name(self, text: str, source_url: str = "") -> str:
        """Extract company name from document"""
        # First try to extract from URL (NSE/BSE format)
        if source_url:
            url_match = re.search(r'/([A-Z]+)_\d+_', source_url)
            if url_match:
                company_code = url_match.group(1)
                # Map common NSE codes to full names
                company_mapping = {
                    'SBICARDS': 'SBI Cards and Payment Services Limited',
                    'RATEGAIN': 'RateGain Travel Technologies Limited',
                    'RELIANCE': 'Reliance Industries Limited',
                    'TCS': 'Tata Consultancy Services Limited',
                    'INFY': 'Infosys Limited',
                    'HDFCBANK': 'HDFC Bank Limited',
                    'ICICIBANK': 'ICICI Bank Limited',
                    'SBIN': 'State Bank of India',
                    'BHARTIARTL': 'Bharti Airtel Limited',
                    'ITC': 'ITC Limited'
                }
                if company_code in company_mapping:
                    return company_mapping[company_code]

        # Look for common patterns in NSE/BSE document content
        patterns = [
            # NSE/BSE specific patterns
            r'(?:^|\n)([A-Z][A-Za-z\s&]+(?:Limited|Ltd\.?|Inc\.?))\s*(?:\n|$)',
            r'Company:\s*([A-Z][A-Za-z\s&]+(?:Limited|Ltd\.?|Inc\.?))',
            r'Subject:\s*([A-Z][A-Za-z\s&]+(?:Limited|Ltd\.?|Inc\.?))',
            r'([A-Z][A-Za-z\s&]+(?:Limited|Ltd\.?))\s*(?:announces|reports|declares)',
            # Look for company name in first line
            r'^([A-Z][A-Za-z\s&]+(?:Limited|Ltd\.?|Inc\.?))',
        ]

        for pattern in patterns:
            match = re.search(pattern, text[:2000], re.MULTILINE)  # Search in first 2000 chars
            if match:
                company_name = match.group(1).strip()
                # Filter out common false positives
                if len(company_name) > 5 and not any(word in company_name.lower() for word in ['press', 'release', 'announcement', 'filing']):
                    return company_name

        # Fallback: try to extract from URL code
        if source_url:
            url_match = re.search(r'/([A-Z]+)_\d+_', source_url)
            if url_match:
                return f"{url_match.group(1)} (Company Code)"

        return "Unknown Company"
    
    def generate_ai_summary(self, text: str, document_type: str) -> Dict[str, Any]:
        """Generate AI-powered summary based on document type"""
        
        # Create specialized prompts based on document type
        prompts = {
            "earnings_call": """
            Analyze this earnings call announcement and extract:
            1. Quarter and Financial Year
            2. Date and time of call
            3. Registration URL or dial-in details
            Summarize in max 50 words unless critical details require more.
            """,
            
            "quarterly_results": """
            Analyze this quarterly results announcement and extract:
            1. Quarter and Financial Year
            2. Key revenue, EBITDA, and PAT numbers with YoY growth %
            3. Management commentary on performance (what went right/wrong)
            Summarize in max 50 words unless critical financial data requires more.
            """,
            
            "merger_acquisition": """
            Analyze this M&A announcement and extract:
            1. Company being acquired and its financials
            2. Deal terms (valuation, cash vs equity)
            3. Whether it's a related party transaction
            4. Purpose/rationale for acquisition
            Summarize in max 50 words unless deal complexity requires more.
            """,
            
            "annual_report": """
            Analyze this annual report announcement and extract:
            1. Financial year covered
            2. Key highlights or availability details
            Summarize in max 50 words.
            """,
            
            "press_release": """
            Analyze this press release and extract:
            1. Main message/announcement (avoid fluff)
            2. Key business impact or implications
            Summarize in max 50 words unless business impact requires more detail.
            """,
            
            "general_announcement": """
            Analyze this corporate announcement and extract:
            1. Main purpose/message
            2. Key business implications
            Summarize in max 50 words unless critical details require more.
            """
        }
        
        system_prompt = """You are an analyst at a world-class investment firm. Analyze corporate announcements from NSE/BSE exchanges. 
        Provide concise, actionable summaries for Partners. Focus on key information, avoid verbosity, get to the point.
        Return response as JSON with 'summary' and 'key_details' fields."""
        
        user_prompt = prompts.get(document_type, prompts["general_announcement"])
        user_prompt += f"\n\nDocument text:\n{text[:4000]}"  # Limit text to avoid token limits
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            # Try to parse as JSON, fallback to structured text
            content = response.choices[0].message.content
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                return {
                    "summary": content[:200] + "..." if len(content) > 200 else content,
                    "key_details": {"raw_analysis": content}
                }
                
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return {
                "summary": "AI analysis unavailable",
                "key_details": {"error": str(e)}
            }
    
    def analyze_document(self, pdf_url: str) -> Dict[str, Any]:
        """Main method to analyze document from PDF URL"""
        try:
            # Download and extract text
            logger.info(f"Downloading PDF from: {pdf_url}")
            pdf_content = self.download_pdf(pdf_url)
            
            logger.info("Extracting text from PDF")
            text = self.extract_text_from_pdf(pdf_content)
            
            if not text.strip():
                raise ValueError("No text could be extracted from PDF")
            
            # Classify and analyze
            document_type = self.classify_document_type(text)
            company_name = self.extract_company_name(text)
            
            logger.info(f"Document type: {document_type}, Company: {company_name}")
            
            # Generate AI summary
            ai_analysis = self.generate_ai_summary(text, document_type)
            
            # Create structured summary
            summary = DocumentSummary(
                document_type=document_type,
                company_name=company_name,
                summary=ai_analysis.get("summary", "Summary unavailable"),
                key_details=ai_analysis.get("key_details", {}),
                analysis_date=datetime.now().isoformat(),
                source_url=pdf_url
            )
            
            return {
                "document_type": summary.document_type,
                "company_name": summary.company_name,
                "summary": summary.summary,
                "key_details": summary.key_details,
                "analysis_date": summary.analysis_date,
                "source_url": summary.source_url,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"Document analysis failed: {e}")
            return {
                "status": "error",
                "error_message": str(e),
                "source_url": pdf_url,
                "analysis_date": datetime.now().isoformat()
            }

def main():
    """Main function for command line usage"""
    import sys
    import os
    
    if len(sys.argv) != 2:
        print("Usage: python document_analyzer.py <pdf_url>")
        sys.exit(1)
    
    # Get OpenAI API key from environment
    api_key = "********************************************************************************************************************************************************************"
    if not api_key:
        print("Error: OPENAI_API_KEY environment variable not set")
        sys.exit(1)
    
    pdf_url = sys.argv[1]
    
    # Initialize analyzer and process document
    analyzer = DocumentAnalyzer(api_key)
    result = analyzer.analyze_document(pdf_url)
    
    # Output JSON result
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
