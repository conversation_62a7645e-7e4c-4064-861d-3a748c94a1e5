#!/usr/bin/env python3
"""
Example usage of the Document Analyzer
"""

import json
import os
from document_analyzer import DocumentAnalyzer

def example_usage():
    """Example of how to use the DocumentAnalyzer"""
    
    # Set your OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("Please set OPENAI_API_KEY environment variable")
        return
    
    # Initialize analyzer
    analyzer = DocumentAnalyzer(api_key)
    
    # Example PDF URLs (replace with actual NSE/BSE announcement URLs)
    example_urls = [
        "https://example.com/earnings-call-announcement.pdf",
        "https://example.com/quarterly-results.pdf",
        "https://example.com/merger-announcement.pdf"
    ]
    
    for url in example_urls:
        print(f"\nAnalyzing: {url}")
        print("-" * 50)
        
        try:
            result = analyzer.analyze_document(url)
            print(json.dumps(result, indent=2, ensure_ascii=False))
        except Exception as e:
            print(f"Error analyzing {url}: {e}")

def analyze_single_document(pdf_url: str):
    """Analyze a single document"""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("Please set OPENAI_API_KEY environment variable")
        return
    
    analyzer = DocumentAnalyzer(api_key)
    result = analyzer.analyze_document(pdf_url)
    
    print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Analyze specific URL provided as argument
        analyze_single_document(sys.argv[1])
    else:
        # Run example usage
        example_usage()
