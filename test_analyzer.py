#!/usr/bin/env python3
"""
Test script for Document Analyzer
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from document_analyzer import DocumentAnalyzer
import json

class TestDocumentAnalyzer(unittest.TestCase):
    """Test cases for DocumentAnalyzer"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.analyzer = DocumentAnalyzer("test-api-key")
    
    def test_classify_document_type(self):
        """Test document type classification"""
        # Test earnings call
        earnings_text = "Earnings call announcement for Q2 FY2024"
        self.assertEqual(self.analyzer.classify_document_type(earnings_text), "earnings_call")
        
        # Test quarterly results
        results_text = "Quarterly results for the period ended March 2024"
        self.assertEqual(self.analyzer.classify_document_type(results_text), "quarterly_results")
        
        # Test M&A
        ma_text = "Acquisition of XYZ Company Limited"
        self.assertEqual(self.analyzer.classify_document_type(ma_text), "merger_acquisition")
        
        # Test annual report
        annual_text = "Annual Report for FY 2023-24"
        self.assertEqual(self.analyzer.classify_document_type(annual_text), "annual_report")
        
        # Test press release
        pr_text = "Press Release: Company announces new product launch"
        self.assertEqual(self.analyzer.classify_document_type(pr_text), "press_release")
    
    def test_extract_company_name(self):
        """Test company name extraction"""
        text1 = "ABC Corporation Limited announces quarterly results"
        self.assertIn("ABC Corporation Limited", self.analyzer.extract_company_name(text1))
        
        text2 = "XYZ Ltd. reports strong performance"
        self.assertIn("XYZ Ltd.", self.analyzer.extract_company_name(text2))
    
    @patch('requests.get')
    def test_download_pdf_success(self, mock_get):
        """Test successful PDF download"""
        mock_response = Mock()
        mock_response.content = b"fake pdf content"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.analyzer.download_pdf("http://example.com/test.pdf")
        self.assertEqual(result, b"fake pdf content")
    
    @patch('requests.get')
    def test_download_pdf_failure(self, mock_get):
        """Test PDF download failure"""
        mock_get.side_effect = Exception("Network error")
        
        with self.assertRaises(Exception):
            self.analyzer.download_pdf("http://example.com/test.pdf")
    
    @patch('PyPDF2.PdfReader')
    def test_extract_text_from_pdf(self, mock_pdf_reader):
        """Test text extraction from PDF"""
        # Mock PDF reader and pages
        mock_page = Mock()
        mock_page.extract_text.return_value = "Sample text from PDF"
        
        mock_reader_instance = Mock()
        mock_reader_instance.pages = [mock_page]
        mock_pdf_reader.return_value = mock_reader_instance
        
        result = self.analyzer.extract_text_from_pdf(b"fake pdf content")
        self.assertEqual(result, "Sample text from PDF")
    
    def test_json_output_structure(self):
        """Test that the output has the expected JSON structure"""
        # Mock the methods to avoid external dependencies
        with patch.object(self.analyzer, 'download_pdf') as mock_download, \
             patch.object(self.analyzer, 'extract_text_from_pdf') as mock_extract, \
             patch.object(self.analyzer, 'generate_ai_summary') as mock_ai:
            
            mock_download.return_value = b"fake content"
            mock_extract.return_value = "Sample earnings call announcement for ABC Ltd Q2 FY2024"
            mock_ai.return_value = {
                "summary": "Earnings call for Q2 FY2024 scheduled",
                "key_details": {"quarter": "Q2 FY2024", "date": "2024-01-15"}
            }
            
            result = self.analyzer.analyze_document("http://example.com/test.pdf")
            
            # Check required fields
            required_fields = ["document_type", "company_name", "summary", "key_details", 
                             "analysis_date", "source_url", "status"]
            for field in required_fields:
                self.assertIn(field, result)
            
            self.assertEqual(result["status"], "success")
            self.assertEqual(result["document_type"], "earnings_call")

def run_integration_test():
    """Run a simple integration test with a real PDF URL (if available)"""
    import os
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("Skipping integration test - OPENAI_API_KEY not set")
        return
    
    # You can replace this with an actual PDF URL for testing
    test_url = "https://example.com/sample.pdf"
    
    analyzer = DocumentAnalyzer(api_key)
    
    try:
        result = analyzer.analyze_document(test_url)
        print("Integration test result:")
        print(json.dumps(result, indent=2))
    except Exception as e:
        print(f"Integration test failed (expected with example URL): {e}")

if __name__ == "__main__":
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    print("Running integration test...")
    run_integration_test()
